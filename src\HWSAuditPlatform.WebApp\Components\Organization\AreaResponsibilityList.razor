@using HWSAuditPlatform.WebApp.Models
@using HWSAuditPlatform.WebApp.Services
@using ResponsibilityType = HWSAuditPlatform.WebApp.Models.ResponsibilityType
@inject IAreaResponsibilityApiService AreaResponsibilityApiService
@inject ILogger<AreaResponsibilityList> Logger

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">Area Responsibilities</h5>
        <button class="btn btn-primary btn-sm" @onclick="ShowCreateModal">
            <i class="fas fa-plus"></i> Add Responsibility
        </button>
    </div>
    <div class="card-body">
        @if (isLoading)
        {
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        }
        else if (responsibilities.Any())
        {
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Area</th>
                            <th>Responsibility Type</th>
                            <th>Responsible User</th>
                            <th>Template</th>
                            <th>Priority</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var responsibility in responsibilities)
                        {
                            <tr>
                                <td>@responsibility.AreaName</td>
                                <td>
                                    <span class="badge bg-info">@GetResponsibilityTypeDisplayName(responsibility.ResponsibilityType)</span>
                                </td>
                                <td>
                                    <div>
                                        <strong>@responsibility.ResponsibleUserFullName</strong>
                                        <br />
                                        <small class="text-muted">@responsibility.ResponsibleUserName</small>
                                    </div>
                                </td>
                                <td>
                                    @if (responsibility.IsTemplateSpecific)
                                    {
                                        <span class="badge bg-secondary">@responsibility.AuditTemplateName</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-light text-dark">General</span>
                                    }
                                </td>
                                <td>@responsibility.Priority</td>
                                <td>
                                    @if (responsibility.IsActive)
                                    {
                                        <span class="badge bg-success">Active</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">Inactive</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" @onclick="() => EditResponsibility(responsibility)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" @onclick="() => DeleteResponsibility(responsibility)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center text-muted">
                <i class="fas fa-users fa-3x mb-3"></i>
                <p>No area responsibilities configured.</p>
                <button class="btn btn-primary" @onclick="ShowCreateModal">
                    <i class="fas fa-plus"></i> Add First Responsibility
                </button>
            </div>
        }
    </div>
</div>

@if (showCreateModal)
{
    <AreaResponsibilityCreateModal 
        @bind-IsVisible="showCreateModal"
        OnResponsibilityCreated="OnResponsibilityCreated" />
}

@code {
    [Parameter] public int? FilterAreaId { get; set; }
    [Parameter] public int? FilterTemplateId { get; set; }
    [Parameter] public EventCallback OnResponsibilityChanged { get; set; }

    private List<AreaResponsibilityModel> responsibilities = new();
    private bool isLoading = true;
    private bool showCreateModal = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadResponsibilities();
    }

    protected override async Task OnParametersSetAsync()
    {
        await LoadResponsibilities();
    }

    private async Task LoadResponsibilities()
    {
        try
        {
            isLoading = true;
            var filter = new AreaResponsibilityFilterModel
            {
                AreaId = FilterAreaId,
                AuditTemplateId = FilterTemplateId,
                IsActive = null // Show both active and inactive
            };

            responsibilities = await AreaResponsibilityApiService.GetAreaResponsibilitiesAsync(filter);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading area responsibilities");
            responsibilities = new List<AreaResponsibilityModel>();
        }
        finally
        {
            isLoading = false;
        }
    }

    private void ShowCreateModal()
    {
        showCreateModal = true;
    }

    private async Task OnResponsibilityCreated()
    {
        showCreateModal = false;
        await LoadResponsibilities();
        await OnResponsibilityChanged.InvokeAsync();
    }

    private async Task EditResponsibility(AreaResponsibilityModel responsibility)
    {
        // TODO: Implement edit functionality
        Logger.LogInformation("Edit responsibility {Id} requested", responsibility.Id);
    }

    private async Task DeleteResponsibility(AreaResponsibilityModel responsibility)
    {
        // TODO: Implement delete functionality with confirmation
        Logger.LogInformation("Delete responsibility {Id} requested", responsibility.Id);
    }

    private static string GetResponsibilityTypeDisplayName(ResponsibilityType type)
    {
        return type switch
        {
            ResponsibilityType.CorrectiveActionOwner => "Corrective Actions",
            ResponsibilityType.RetrospectiveAnalyst => "Retrospective Analysis",
            ResponsibilityType.FindingReviewer => "Finding Review",
            ResponsibilityType.EscalationContact => "Escalation Contact",
            _ => type.ToString()
        };
    }
}
