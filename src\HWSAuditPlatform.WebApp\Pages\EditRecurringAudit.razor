@page "/recurring-audits/{id}/edit"
@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.WebApp.Models
@using HWSAuditPlatform.Domain.Enums
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize(Roles = "<PERSON>O<PERSON><PERSON>,SystemManager,DevAdmin")]
@inject IRecurringAuditApiService RecurringAuditService
@inject ITemplateApiService TemplateService
@inject IOrganizationApiService OrganizationService
@inject IUserApiService UserService
@inject NavigationManager Navigation
@inject ILogger<EditRecurringAudit> Logger

<PageTitle>Edit Recurring Audit - HWS Audit Platform</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">
                        <i class="bi bi-pencil-square me-2"></i>
                        Edit Recurring Audit Schedule
                    </h2>
                    <p class="text-muted mb-0">Modify automated audit scheduling</p>
                </div>
                <button class="btn btn-outline-secondary" @onclick="GoBack">
                    <i class="bi bi-arrow-left me-2"></i>
                    Back to List
                </button>
            </div>

            @if (isLoading)
            {
                <div class="d-flex justify-content-center align-items-center" style="min-height: 400px;">
                    <div class="text-center">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="text-muted">Loading recurring audit settings...</p>
                    </div>
                </div>
            }
            else if (existingSetting == null)
            {
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Recurring audit setting not found or you don't have permission to edit it.
                </div>
            }
            else
            {
                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        @errorMessage
                        <button type="button" class="btn-close" @onclick="() => errorMessage = null"></button>
                    </div>
                }

                @if (!string.IsNullOrEmpty(successMessage))
                {
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bi bi-check-circle me-2"></i>
                        @successMessage
                        <button type="button" class="btn-close" @onclick="() => successMessage = null"></button>
                    </div>
                }

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-gear me-2"></i>
                            Audit Schedule Configuration
                        </h5>
                    </div>
                    <div class="card-body">
                        <EditForm Model="model" OnValidSubmit="HandleValidSubmit">
                            <DataAnnotationsValidator />

                            <div class="row">
                                <!-- Basic Information -->
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="settingName" class="form-label">
                                            <i class="bi bi-tag me-1"></i>
                                            Schedule Name <span class="text-danger">*</span>
                                        </label>
                                        <InputText id="settingName" class="form-control" @bind-Value="model.SettingName" placeholder="Enter a descriptive name for this schedule" />
                                        <ValidationMessage For="@(() => model.SettingName)" />
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="deadlineDays" class="form-label">
                                            <i class="bi bi-calendar-check me-1"></i>
                                            Deadline Days <span class="text-danger">*</span>
                                        </label>
                                        <InputNumber id="deadlineDays" class="form-control" @bind-Value="model.DeadlineDays" min="1" max="365" />
                                        <div class="form-text">Number of days from scheduled date to audit deadline</div>
                                        <ValidationMessage For="@(() => model.DeadlineDays)" />
                                    </div>
                                </div>
                            </div>

                            <!-- Template Information (Read-only) -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label">
                                            <i class="bi bi-file-text me-1"></i>
                                            Audit Template
                                        </label>
                                        <div class="form-control-plaintext bg-light p-2 rounded">
                                            <strong>@existingSetting.AuditTemplateName</strong>
                                            <small class="text-muted d-block">Template cannot be changed after creation</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Location Information (Read-only) -->
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">
                                            <i class="bi bi-building me-1"></i>
                                            Factory
                                        </label>
                                        <div class="form-control-plaintext bg-light p-2 rounded">
                                            @existingSetting.FactoryName
                                            <small class="text-muted d-block">Location cannot be changed after creation</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">
                                            <i class="bi bi-geo-alt me-1"></i>
                                            Area
                                        </label>
                                        <div class="form-control-plaintext bg-light p-2 rounded">
                                            @(existingSetting.AreaName ?? "All Areas")
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">
                                            <i class="bi bi-pin-map me-1"></i>
                                            Sub-Area
                                        </label>
                                        <div class="form-control-plaintext bg-light p-2 rounded">
                                            @(existingSetting.SubAreaName ?? "All Sub-Areas")
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Assignment Configuration -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="assignmentType" class="form-label">
                                            <i class="bi bi-person-check me-1"></i>
                                            Assignment Type <span class="text-danger">*</span>
                                        </label>
                                        <InputSelect id="assignmentType" class="form-select" @bind-Value="model.AssignmentType" disabled="@isSubmitting" @onchange="OnAssignmentTypeChangedHandler">
                                            <option value="">Select assignment type...</option>
                                            <option value="@AssignmentType.Individual">Individual Assignment</option>
                                            <option value="@AssignmentType.GroupAny">Group Assignment (Any Member)</option>
                                            <option value="@AssignmentType.GroupAllScheduled">Group Assignment (All Members)</option>
                                        </InputSelect>
                                        <ValidationMessage For="@(() => model.AssignmentType)" />
                                    </div>
                                </div>

                                @if (model.AssignmentType == AssignmentType.Individual)
                                {
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="assignToUserId" class="form-label">
                                                <i class="bi bi-person me-1"></i>
                                                Assign to User <span class="text-danger">*</span>
                                            </label>
                                            <InputSelect id="assignToUserId" class="form-select" @bind-Value="model.AssignToUserId" disabled="@(isLoadingUsers || isSubmitting)">
                                                <option value="">@(isLoadingUsers ? "Loading users..." : "Select user...")</option>
                                                @if (availableUsers != null)
                                                {
                                                    @foreach (var user in availableUsers)
                                                    {
                                                        <option value="@user.AdObjectGuid">@user.DisplayName (@user.Username)</option>
                                                    }
                                                }
                                            </InputSelect>
                                            <ValidationMessage For="@(() => model.AssignToUserId)" />
                                        </div>
                                    </div>
                                }
                                else if (model.AssignmentType == AssignmentType.GroupAny || model.AssignmentType == AssignmentType.GroupAllScheduled)
                                {
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="assignToUserGroupId" class="form-label">
                                                <i class="bi bi-people me-1"></i>
                                                Assign to Group <span class="text-danger">*</span>
                                            </label>
                                            <InputSelect id="assignToUserGroupId" class="form-select" @bind-Value="model.AssignToUserGroupId" disabled="@(isLoadingUserGroups || isSubmitting)">
                                                <option value="">@(isLoadingUserGroups ? "Loading groups..." : "Select group...")</option>
                                                @if (availableUserGroups != null)
                                                {
                                                    @foreach (var group in availableUserGroups)
                                                    {
                                                        <option value="@group.Id">@group.Name</option>
                                                    }
                                                }
                                            </InputSelect>
                                            <ValidationMessage For="@(() => model.AssignToUserGroupId)" />
                                        </div>
                                    </div>
                                }
                            </div>

                            <!-- Recurrence Configuration -->
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="bi bi-arrow-repeat me-2"></i>
                                        Recurrence Schedule
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="frequencyType" class="form-label">
                                                    <i class="bi bi-calendar-event me-1"></i>
                                                    Frequency <span class="text-danger">*</span>
                                                </label>
                                                <InputSelect id="frequencyType" class="form-select" @bind-Value="model.RecurrenceRule.FrequencyType" disabled="@isSubmitting" @onchange="OnFrequencyChangedHandler">
                                                    <option value="">Select frequency...</option>
                                                    <option value="@FrequencyType.DAILY">Daily</option>
                                                    <option value="@FrequencyType.WEEKLY">Weekly</option>
                                                    <option value="@FrequencyType.MONTHLY">Monthly</option>
                                                    <option value="@FrequencyType.YEARLY">Yearly</option>
                                                </InputSelect>
                                                <ValidationMessage For="@(() => model.RecurrenceRule.FrequencyType)" />
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="interval" class="form-label">
                                                    <i class="bi bi-arrow-clockwise me-1"></i>
                                                    Interval <span class="text-danger">*</span>
                                                </label>
                                                <InputNumber id="interval" class="form-control" @bind-Value="model.RecurrenceRule.Interval" min="1" max="999" disabled="@isSubmitting" />
                                                <div class="form-text">
                                                    @GetIntervalDescription()
                                                </div>
                                                <ValidationMessage For="@(() => model.RecurrenceRule.Interval)" />
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="endDate" class="form-label">
                                                    <i class="bi bi-calendar-x me-1"></i>
                                                    End Date (Optional)
                                                </label>
                                                <InputDate id="endDate" class="form-control" @bind-Value="model.RecurrenceRule.EndDate" disabled="@isSubmitting" />
                                                <div class="form-text">Leave empty for indefinite recurrence</div>
                                                <ValidationMessage For="@(() => model.RecurrenceRule.EndDate)" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="d-flex justify-content-between mt-4">
                                <button type="button" class="btn btn-outline-secondary" @onclick="GoBack" disabled="@isSubmitting">
                                    <i class="bi bi-x-lg me-2"></i>
                                    Cancel
                                </button>
                                <button type="submit" class="btn btn-primary" disabled="@isSubmitting">
                                    @if (isSubmitting)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                        <span>Updating...</span>
                                    }
                                    else
                                    {
                                        <i class="bi bi-check-lg me-2"></i>
                                        <span>Update Schedule</span>
                                    }
                                </button>
                            </div>
                        </EditForm>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@code {
    [Parameter] public string Id { get; set; } = string.Empty;

    private UpdateRecurringAuditSettingRequest model = new();
    private RecurringAuditSettingDto? existingSetting;
    private List<UserSummaryDto>? availableUsers;
    private List<UserGroupDto>? availableUserGroups;

    private bool isLoading = true;
    private bool isLoadingUsers = false;
    private bool isLoadingUserGroups = false;
    private bool isSubmitting = false;
    private string? errorMessage;
    private string? successMessage;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            await LoadExistingSettingAsync();
            if (existingSetting != null)
            {
                await LoadInitialDataAsync();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error initializing edit recurring audit page");
            errorMessage = "Failed to load recurring audit settings. Please try again.";
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task LoadExistingSettingAsync()
    {
        try
        {
            existingSetting = await RecurringAuditService.GetRecurringAuditSettingByIdAsync(Id);

            if (existingSetting != null)
            {
                // Populate the model with existing values
                model = new UpdateRecurringAuditSettingRequest
                {
                    SettingName = existingSetting.SettingName,
                    DeadlineDays = existingSetting.DeadlineDays,
                    AssignmentType = existingSetting.AssignmentType,
                    AssignToUserId = existingSetting.AssignToUserId,
                    AssignToUserGroupId = existingSetting.AssignToUserGroupId,
                    RecurrenceRule = new UpdateRecurrenceRuleRequest
                    {
                        FrequencyType = existingSetting.RecurrenceRule.FrequencyType,
                        Interval = existingSetting.RecurrenceRule.Interval,
                        EndDate = existingSetting.RecurrenceRule.EndDate,
                        DayOfMonth = existingSetting.RecurrenceRule.DayOfMonth,
                        DayOfWeek = existingSetting.RecurrenceRule.DayOfWeek,
                        WeekOfMonth = existingSetting.RecurrenceRule.WeekOfMonth,
                        MonthOfYear = existingSetting.RecurrenceRule.MonthOfYear,
                        CustomPattern = existingSetting.RecurrenceRule.CustomPattern
                    }
                };
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading existing recurring audit setting {Id}", Id);
            throw;
        }
    }

    private async Task LoadInitialDataAsync()
    {
        // Load users and groups based on assignment type
        if (model.AssignmentType == AssignmentType.Individual)
        {
            await LoadUsersAsync();
        }
        else if (model.AssignmentType == AssignmentType.GroupAny || model.AssignmentType == AssignmentType.GroupAllScheduled)
        {
            await LoadUserGroupsAsync();
        }
    }

    private async Task LoadUsersAsync()
    {
        if (isLoadingUsers) return;

        try
        {
            isLoadingUsers = true;
            var result = await OrganizationService.GetUsersAsync(1, 100);
            availableUsers = result?.Items?.ToList();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading users");
            errorMessage = "Failed to load users. Please try again.";
        }
        finally
        {
            isLoadingUsers = false;
        }
    }

    private async Task LoadUserGroupsAsync()
    {
        if (isLoadingUserGroups) return;

        try
        {
            isLoadingUserGroups = true;
            availableUserGroups = await UserService.GetUserGroupsAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading user groups");
            errorMessage = "Failed to load user groups. Please try again.";
        }
        finally
        {
            isLoadingUserGroups = false;
        }
    }

    private async Task OnAssignmentTypeChangedHandler(ChangeEventArgs e)
    {
        if (Enum.TryParse<AssignmentType>(e.Value?.ToString(), out var assignmentType))
        {
            model.AssignmentType = assignmentType;

            // Clear previous assignments
            model.AssignToUserId = null;
            model.AssignToUserGroupId = null;

            // Load appropriate data
            if (assignmentType == AssignmentType.Individual)
            {
                await LoadUsersAsync();
            }
            else if (assignmentType == AssignmentType.GroupAny || assignmentType == AssignmentType.GroupAllScheduled)
            {
                await LoadUserGroupsAsync();
            }
        }
    }

    private void OnFrequencyChangedHandler(ChangeEventArgs e)
    {
        if (Enum.TryParse<FrequencyType>(e.Value?.ToString(), out var frequencyType))
        {
            model.RecurrenceRule.FrequencyType = frequencyType;

            // Reset frequency-specific fields
            model.RecurrenceRule.DayOfMonth = null;
            model.RecurrenceRule.DayOfWeek = null;
            model.RecurrenceRule.WeekOfMonth = null;
            model.RecurrenceRule.MonthOfYear = null;
            model.RecurrenceRule.CustomPattern = null;
        }
    }

    private string GetIntervalDescription()
    {
        return model.RecurrenceRule.FrequencyType switch
        {
            FrequencyType.DAILY => $"Every {model.RecurrenceRule.Interval} day(s)",
            FrequencyType.WEEKLY => $"Every {model.RecurrenceRule.Interval} week(s)",
            FrequencyType.MONTHLY => $"Every {model.RecurrenceRule.Interval} month(s)",
            FrequencyType.YEARLY => $"Every {model.RecurrenceRule.Interval} year(s)",
            _ => "Select frequency first"
        };
    }

    private async Task HandleValidSubmit()
    {
        if (isSubmitting || existingSetting == null) return;

        try
        {
            isSubmitting = true;
            errorMessage = null;
            successMessage = null;

            var success = await RecurringAuditService.UpdateRecurringAuditSettingAsync(Id, model);

            if (success)
            {
                successMessage = "Recurring audit schedule updated successfully!";
                await Task.Delay(1500); // Show success message briefly
                Navigation.NavigateTo("/recurring-audits");
            }
            else
            {
                errorMessage = "Failed to update recurring audit schedule. Please check your input and try again.";
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error updating recurring audit setting");
            errorMessage = "An error occurred while updating the recurring audit schedule. Please try again.";
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/recurring-audits");
    }
}
