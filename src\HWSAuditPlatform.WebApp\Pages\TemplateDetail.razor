@page "/templates/{id:int}"
@using HWSAuditPlatform.Domain.Entities.Templates
@using HWSAuditPlatform.Domain.Enums
@using HWSAuditPlatform.WebApp.Services
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject ITemplateApiService TemplateService
@inject ITemplateValidationService ValidationService
@inject NavigationManager Navigation
@inject ILogger<TemplateDetail> Logger
@inject IJSRuntime JSRuntime

<PageTitle>Template Details - HWS Audit Platform</PageTitle>

<div class="container-fluid mt-4">
    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading template details...</p>
        </div>
    }
    else if (template == null)
    {
        <div class="alert alert-danger" role="alert">
            <h4 class="alert-heading">Template Not Found</h4>
            <p>The requested template could not be found or you don't have permission to view it.</p>
            <hr>
            <button class="btn btn-outline-danger" @onclick="BackToTemplates">
                <i class="bi bi-arrow-left me-2"></i>Back to Templates
            </button>
        </div>
    }
    else
    {
        <!-- Template Header -->
        <div class="row mb-4">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="/templates" class="text-decoration-none">Templates</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">@template.TemplateName</li>
                    </ol>
                </nav>
                
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h1 class="h2 mb-2">@template.TemplateName</h1>
                        <div class="d-flex align-items-center gap-3 mb-3">
                            <span class="badge @(template.IsPublished ? "bg-success" : "bg-warning") fs-6">
                                @(template.IsPublished ? "Published" : "Draft")
                            </span>
                            <span class="badge bg-secondary fs-6">Version @template.Version</span>
                            @if (!template.IsActive)
                            {
                                <span class="badge bg-danger fs-6">Inactive</span>
                            }
                        </div>
                    </div>
                    
                    <div class="btn-group" role="group">
                        @if (template.IsPublished)
                        {
                            <button class="btn btn-primary" @onclick="CreateNewVersion">
                                <i class="bi bi-plus-circle me-2"></i>Create New Version
                            </button>
                        }
                        else
                        {
                            <button class="btn btn-outline-primary" @onclick="EditTemplate">
                                <i class="bi bi-pencil me-2"></i>Edit Template
                            </button>
                            <button class="btn btn-success" @onclick="PublishTemplate" disabled="@(!CanPublish())">
                                <i class="bi bi-check-circle me-2"></i>Publish
                            </button>
                        }
                        <div class="btn-group" role="group">
                            <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <button class="dropdown-item" @onclick="ViewVersionHistory">
                                        <i class="bi bi-clock-history me-2"></i>Version History
                                    </button>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <button class="dropdown-item text-danger" @onclick="DeleteTemplate">
                                        <i class="bi bi-trash me-2"></i>Delete Template
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Template Information -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-info-circle me-2"></i>Template Information
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (!string.IsNullOrEmpty(template.Description))
                        {
                            <p class="card-text">@template.Description</p>
                        }
                        else
                        {
                            <p class="card-text text-muted fst-italic">No description provided</p>
                        }
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <small class="text-muted d-block">Created</small>
                                <span>@template.CreatedAt.ToString("MMM dd, yyyy 'at' HH:mm")</span>
                            </div>
                            <div class="col-md-6">
                                <small class="text-muted d-block">Last Updated</small>
                                <span>@template.UpdatedAt.ToString("MMM dd, yyyy 'at' HH:mm")</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-bar-chart me-2"></i>Statistics
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h4 class="text-primary mb-1">@GetTotalQuestionCount()</h4>
                                    <small class="text-muted">Questions</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h4 class="text-info mb-1">@template.QuestionGroups.Count(g => g.IsActive)</h4>
                                <small class="text-muted">Groups</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Questions Section -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-question-circle me-2"></i>Questions
                </h5>
                @if (!template.IsPublished)
                {
                    <button class="btn btn-sm btn-outline-primary" @onclick="ManageQuestions">
                        <i class="bi bi-gear me-1"></i>Manage Questions
                    </button>
                }
            </div>
            <div class="card-body">
                @if (GetTotalQuestionCount() > 0)
                {
                    @if (template.QuestionGroups.Any(g => g.IsActive))
                    {
                        <!-- Grouped Questions -->
                        @foreach (var group in template.QuestionGroups.Where(g => g.IsActive).OrderBy(g => g.DisplayOrder))
                        {
                            <div class="question-group mb-4">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="bi bi-folder me-2"></i>@group.GroupName
                                    @if (!string.IsNullOrEmpty(group.Description))
                                    {
                                        <small class="text-muted ms-2">- @group.Description</small>
                                    }
                                </h6>
                                
                                @foreach (var question in group.Questions.Where(q => q.IsActive).OrderBy(q => q.DisplayOrder))
                                {
                                    <div class="question-item mb-3 p-3 border rounded">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="flex-grow-1">
                                                <h6 class="mb-2">@question.QuestionText</h6>
                                                <div class="d-flex align-items-center gap-3 mb-2">
                                                    <span class="badge bg-light text-dark">@question.QuestionType</span>
                                                    @if (question.IsRequired)
                                                    {
                                                        <span class="badge bg-danger">Required</span>
                                                    }
                                                    @if (question.EvidenceRequired)
                                                    {
                                                        <span class="badge bg-warning">Evidence Required</span>
                                                    }
                                                    @if (question.SeverityLevel.HasValue)
                                                    {
                                                        <span class="badge bg-info">@question.SeverityLevel</span>
                                                    }
                                                </div>
                                                
                                                @if (!string.IsNullOrEmpty(question.HelpText))
                                                {
                                                    <p class="text-muted small mb-2">
                                                        <i class="bi bi-info-circle me-1"></i>@question.HelpText
                                                    </p>
                                                }
                                                
                                                @if (question.Options.Any(o => o.IsActive))
                                                {
                                                    <div class="mt-2">
                                                        <small class="text-muted d-block mb-1">Options:</small>
                                                        <ul class="list-unstyled ms-3">
                                                            @foreach (var option in question.Options.Where(o => o.IsActive).OrderBy(o => o.DisplayOrder))
                                                            {
                                                                <li class="mb-1">
                                                                    <i class="bi bi-dot me-1"></i>@option.OptionText
                                                                    @if (!string.IsNullOrEmpty(option.OptionValue))
                                                                    {
                                                                        <small class="text-muted">(@option.OptionValue)</small>
                                                                    }
                                                                </li>
                                                            }
                                                        </ul>
                                                    </div>
                                                }
                                            </div>
                                            <div class="text-end">
                                                <small class="text-muted">#@question.DisplayOrder</small>
                                                @if (question.Weight.HasValue)
                                                {
                                                    <br><small class="text-muted">Weight: @question.Weight</small>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                    }
                    
                    <!-- Ungrouped Questions -->
                    @if (template.Questions.Any(q => q.IsActive && !q.QuestionGroupId.HasValue))
                    {
                        <div class="question-group mb-4">
                            <h6 class="text-secondary border-bottom pb-2 mb-3">
                                <i class="bi bi-list me-2"></i>General Questions
                            </h6>
                            
                            @foreach (var question in template.Questions.Where(q => q.IsActive && !q.QuestionGroupId.HasValue).OrderBy(q => q.DisplayOrder))
                            {
                                <div class="question-item mb-3 p-3 border rounded">
                                    <!-- Same question display structure as above -->
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-2">@question.QuestionText</h6>
                                            <div class="d-flex align-items-center gap-3 mb-2">
                                                <span class="badge bg-light text-dark">@question.QuestionType</span>
                                                @if (question.IsRequired)
                                                {
                                                    <span class="badge bg-danger">Required</span>
                                                }
                                                @if (question.EvidenceRequired)
                                                {
                                                    <span class="badge bg-warning">Evidence Required</span>
                                                }
                                                @if (question.SeverityLevel.HasValue)
                                                {
                                                    <span class="badge bg-info">@question.SeverityLevel</span>
                                                }
                                            </div>
                                            
                                            @if (!string.IsNullOrEmpty(question.HelpText))
                                            {
                                                <p class="text-muted small mb-2">
                                                    <i class="bi bi-info-circle me-1"></i>@question.HelpText
                                                </p>
                                            }
                                            
                                            @if (question.Options.Any(o => o.IsActive))
                                            {
                                                <div class="mt-2">
                                                    <small class="text-muted d-block mb-1">Options:</small>
                                                    <ul class="list-unstyled ms-3">
                                                        @foreach (var option in question.Options.Where(o => o.IsActive).OrderBy(o => o.DisplayOrder))
                                                        {
                                                            <li class="mb-1">
                                                                <i class="bi bi-dot me-1"></i>@option.OptionText
                                                                @if (!string.IsNullOrEmpty(option.OptionValue))
                                                                {
                                                                    <small class="text-muted">(@option.OptionValue)</small>
                                                                }
                                                            </li>
                                                        }
                                                    </ul>
                                                </div>
                                            }
                                        </div>
                                        <div class="text-end">
                                            <small class="text-muted">#@question.DisplayOrder</small>
                                            @if (question.Weight.HasValue)
                                            {
                                                <br><small class="text-muted">Weight: @question.Weight</small>
                                            }
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                }
                else
                {
                    <div class="text-center py-4">
                        <i class="bi bi-question-circle display-1 text-muted mb-3"></i>
                        <h5>No Questions</h5>
                        <p class="text-muted">This template doesn't have any questions yet.</p>
                        @if (!template.IsPublished)
                        {
                            <button class="btn btn-primary" @onclick="ManageQuestions">
                                <i class="bi bi-plus-circle me-2"></i>Add Questions
                            </button>
                        }
                    </div>
                }
            </div>
        </div>
    }
</div>

@code {
    [Parameter] public int Id { get; set; }

    private AuditTemplate? template;
    private bool isLoading = true;
    private string? errorMessage = null;

    protected override async Task OnInitializedAsync()
    {
        await LoadTemplate();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (Id > 0)
        {
            await LoadTemplate();
        }
    }

    private async Task LoadTemplate()
    {
        try
        {
            isLoading = true;
            errorMessage = null;
            template = await TemplateService.GetTemplateByIdAsync(Id);

            if (template == null)
            {
                errorMessage = "Template not found.";
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading template {TemplateId}", Id);
            errorMessage = "Failed to load template details. Please try again.";
        }
        finally
        {
            isLoading = false;
        }
    }

    private int GetTotalQuestionCount()
    {
        if (template?.QuestionGroups?.Any() == true)
        {
            return template.QuestionGroups.Sum(g => g.Questions?.Count(q => q.IsActive) ?? 0) +
                   template.Questions.Count(q => q.IsActive && !q.QuestionGroupId.HasValue);
        }
        return template?.Questions?.Count(q => q.IsActive) ?? 0;
    }

    private bool CanPublish()
    {
        return template != null && !template.IsPublished && GetTotalQuestionCount() > 0;
    }

    private void BackToTemplates()
    {
        Navigation.NavigateTo("/templates");
    }

    private void EditTemplate()
    {
        Navigation.NavigateTo($"/templates/{Id}/edit");
    }

    private void ManageQuestions()
    {
        Navigation.NavigateTo($"/templates/{Id}/questions");
    }

    private async Task CreateNewVersion()
    {
        try
        {
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
                "Create a new version of this template? This will copy all questions and settings to a new draft version.");

            if (confirmed)
            {
                var newTemplateId = await TemplateService.CreateTemplateVersionAsync(Id);
                if (newTemplateId.HasValue)
                {
                    Navigation.NavigateTo($"/templates/{newTemplateId.Value}/edit");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Failed to create template version. Please try again.");
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating template version for template {TemplateId}", Id);
            await JSRuntime.InvokeVoidAsync("alert", "An error occurred while creating the template version.");
        }
    }

    private async Task PublishTemplate()
    {
        try
        {
            if (template == null) return;

            // Validate template before publishing
            var validationResult = await ValidationService.ValidateForPublishingAsync(template);

            if (!validationResult.IsValid)
            {
                var errorList = string.Join("\n• ", validationResult.Errors);
                await JSRuntime.InvokeVoidAsync("alert", $"Cannot publish template due to the following errors:\n\n• {errorList}");
                return;
            }

            // Show warnings if any
            if (validationResult.Warnings.Any())
            {
                var warningList = string.Join("\n• ", validationResult.Warnings);
                var proceedWithWarnings = await JSRuntime.InvokeAsync<bool>("confirm",
                    $"The following warnings were found:\n\n• {warningList}\n\nDo you want to proceed with publishing?");

                if (!proceedWithWarnings)
                {
                    return;
                }
            }

            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
                "Publish this template? Once published, it cannot be edited directly. You'll need to create a new version for changes.");

            if (confirmed)
            {
                var result = await TemplateService.PublishTemplateWithDetailsAsync(Id);
                if (result.IsSuccess)
                {
                    await LoadTemplate(); // Refresh to show updated status
                    await JSRuntime.InvokeVoidAsync("alert", "Template published successfully!");
                }
                else
                {
                    if (result.IsValidationError)
                    {
                        // Show validation errors in a more user-friendly way
                        var errorList = string.Join("\n• ", result.ErrorMessages);
                        await JSRuntime.InvokeVoidAsync("alert", $"Cannot publish template due to validation errors:\n\n• {errorList}");
                    }
                    else
                    {
                        await JSRuntime.InvokeVoidAsync("alert", $"Failed to publish template: {result.GetErrorMessage()}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error publishing template {TemplateId}", Id);
            await JSRuntime.InvokeVoidAsync("alert", "An error occurred while publishing the template.");
        }
    }

    private void ViewVersionHistory()
    {
        Navigation.NavigateTo($"/templates/{Id}/versions");
    }

    private async Task DeleteTemplate()
    {
        try
        {
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
                "Are you sure you want to delete this template? This action cannot be undone.");

            if (confirmed)
            {
                var success = await TemplateService.DeleteTemplateAsync(Id);
                if (success)
                {
                    Navigation.NavigateTo("/templates");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Failed to delete template. Please try again.");
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error deleting template {TemplateId}", Id);
            await JSRuntime.InvokeVoidAsync("alert", "An error occurred while deleting the template.");
        }
    }
}
