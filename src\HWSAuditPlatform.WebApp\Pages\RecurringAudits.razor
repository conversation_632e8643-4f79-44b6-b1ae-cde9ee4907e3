@page "/recurring-audits"
@using HWSAuditPlatform.Application.Scheduling.DTOs
@using HWSAuditPlatform.Application.Common
@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.WebApp.Components
@using HWSAuditPlatform.Domain.Enums
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize(Roles = "ProcessOwner,SystemManager,DevAdmin")]
@inject IRecurringAuditApiService RecurringAuditService
@inject NavigationManager Navigation
@inject ILogger<RecurringAudits> Logger

<PageTitle>Recurring Audits - HWS Audit Platform</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">
                        <i class="bi bi-arrow-repeat me-2"></i>
                        Recurring Audits
                    </h2>
                    <p class="text-muted mb-0">Manage automated audit scheduling</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" @onclick="RefreshData" disabled="@isLoading">
                        @if (isLoading)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        else
                        {
                            <i class="bi bi-arrow-clockwise me-2"></i>
                        }
                        Refresh
                    </button>
                    <button class="btn btn-success" @onclick="CreateNewSetting">
                        <i class="bi bi-plus-lg me-2"></i>
                        New Schedule
                    </button>
                </div>
            </div>

            <!-- Messages -->
            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    @errorMessage
                    <button type="button" class="btn-close" @onclick="() => errorMessage = null"></button>
                </div>
            }

            @if (!string.IsNullOrEmpty(successMessage))
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i>
                    @successMessage
                    <button type="button" class="btn-close" @onclick="() => successMessage = null"></button>
                </div>
            }

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">Search</label>
                            <input type="text" class="form-control" @bind="searchTerm" @bind:after="OnSearchChanged"
                                   placeholder="Search by name or template..." />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Status</label>
                            <select class="form-select" @bind="selectedStatus" @bind:after="OnFilterChanged">
                                <option value="">All</option>
                                <option value="true">Enabled</option>
                                <option value="false">Disabled</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Template</label>
                            <select class="form-select" @bind="selectedTemplateId" @bind:after="OnFilterChanged">
                                <option value="">All Templates</option>
                                @* TODO: Load templates from API *@
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button class="btn btn-outline-secondary w-100" @onclick="ClearFilters">
                                <i class="bi bi-x-lg me-1"></i>
                                Clear
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results -->
            @if (isLoading)
            {
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading recurring audit settings...</p>
                </div>
            }
            else if (recurringAudits?.Items?.Any() == true)
            {
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Setting Name</th>
                                        <th>Template</th>
                                        <th>Location</th>
                                        <th>Assignment</th>
                                        <th>Schedule</th>
                                        <th>Status</th>
                                        <th>Next Generation</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var setting in recurringAudits.Items)
                                    {
                                        <tr class="@(setting.IsEnabled ? "" : "table-secondary")">
                                            <td>
                                                <div class="fw-semibold">@setting.SettingName</div>
                                            </td>
                                            <td>
                                                <div>@(setting.AuditTemplateName ?? "Unknown Template")</div>
                                            </td>
                                            <td>
                                                <div>@(setting.FactoryName ?? "Unknown Factory")</div>
                                                @if (!string.IsNullOrEmpty(setting.AreaName))
                                                {
                                                    <small class="text-muted d-block">@setting.AreaName</small>
                                                }
                                                @if (!string.IsNullOrEmpty(setting.SubAreaName))
                                                {
                                                    <small class="text-muted d-block">@setting.SubAreaName</small>
                                                }
                                            </td>
                                            <td>
                                                <div>
                                                    @if (setting.AssignmentType == AssignmentType.Individual)
                                                    {
                                                        <i class="bi bi-person me-1"></i>
                                                        <span>@(setting.AssignToUserName ?? "Unknown User")</span>
                                                    }
                                                    else
                                                    {
                                                        <i class="bi bi-people me-1"></i>
                                                        <span>@(setting.AssignToUserGroupName ?? "Unknown Group")</span>
                                                        @if (setting.AssignmentType == AssignmentType.GroupAllScheduled)
                                                        {
                                                            <small class="text-muted d-block">All members</small>
                                                        }
                                                        else
                                                        {
                                                            <small class="text-muted d-block">Any member</small>
                                                        }
                                                    }
                                                </div>
                                            </td>
                                            <td>
                                                <div>@setting.RecurrenceDescription</div>
                                            </td>
                                            <td>
                                                <span class="badge @(setting.IsEnabled ? "bg-success" : "bg-secondary")">
                                                    @(setting.IsEnabled ? "Enabled" : "Disabled")
                                                </span>
                                                @if (setting.IsReadyToGenerate && setting.IsEnabled)
                                                {
                                                    <span class="badge bg-warning ms-1">Due</span>
                                                }
                                            </td>
                                            <td>
                                                @if (setting.NextGenerationDate.HasValue)
                                                {
                                                    <div>@setting.NextGenerationDate.Value.ToString("MMM dd, yyyy")</div>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Not scheduled</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" @onclick="() => ViewSetting(setting.Id)"
                                                            title="View details">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-secondary" @onclick="() => EditSetting(setting.Id)"
                                                            title="Edit setting">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button class="btn @(setting.IsEnabled ? "btn-outline-warning" : "btn-outline-success")"
                                                            @onclick="() => ToggleSetting(setting.Id, setting.IsEnabled)"
                                                            title="@(setting.IsEnabled ? "Disable" : "Enable")">
                                                        <i class="bi @(setting.IsEnabled ? "bi-pause" : "bi-play")"></i>
                                                    </button>
                                                    @if (setting.IsReadyToGenerate && setting.IsEnabled)
                                                    {
                                                        <button class="btn btn-outline-info" @onclick="() => GenerateNow(setting.Id)"
                                                                title="Generate audits now">
                                                            <i class="bi bi-lightning"></i>
                                                        </button>
                                                    }
                                                    <button class="btn btn-outline-danger" @onclick="() => DeleteSetting(setting.Id)"
                                                            title="Delete setting">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if (recurringAudits.TotalPages > 1)
                        {
                            <nav aria-label="Recurring audits pagination" class="mt-3">
                                <ul class="pagination justify-content-center">
                                    <li class="page-item @(recurringAudits.PageNumber <= 1 ? "disabled" : "")">
                                        <button class="page-link" @onclick="() => ChangePage(recurringAudits.PageNumber - 1)"
                                                disabled="@(recurringAudits.PageNumber <= 1)">
                                            Previous
                                        </button>
                                    </li>
                                    @for (int i = Math.Max(1, recurringAudits.PageNumber - 2); i <= Math.Min(recurringAudits.TotalPages, recurringAudits.PageNumber + 2); i++)
                                    {
                                        <li class="page-item @(i == recurringAudits.PageNumber ? "active" : "")">
                                            <button class="page-link" @onclick="() => ChangePage(i)">@i</button>
                                        </li>
                                    }
                                    <li class="page-item @(recurringAudits.PageNumber >= recurringAudits.TotalPages ? "disabled" : "")">
                                        <button class="page-link" @onclick="() => ChangePage(recurringAudits.PageNumber + 1)"
                                                disabled="@(recurringAudits.PageNumber >= recurringAudits.TotalPages)">
                                            Next
                                        </button>
                                    </li>
                                </ul>
                            </nav>
                        }
                    </div>
                </div>
            }
            else
            {
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="bi bi-arrow-repeat display-1 text-muted mb-3"></i>
                        <h4>No Recurring Audits Found</h4>
                        <p class="text-muted mb-4">
                            @if (HasActiveFilters())
                            {
                                <span>No recurring audit settings match your current filters.</span>
                            }
                            else
                            {
                                <span>You haven't created any recurring audit schedules yet.</span>
                            }
                        </p>
                        @if (HasActiveFilters())
                        {
                            <button class="btn btn-outline-secondary me-2" @onclick="ClearFilters">
                                <i class="bi bi-x-lg me-2"></i>Clear Filters
                            </button>
                        }
                        <button class="btn btn-primary" @onclick="CreateNewSetting">
                            <i class="bi bi-plus-lg me-2"></i>Create First Schedule
                        </button>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Confirmation Dialogs -->
<ConfirmationDialog IsVisible="showDeleteConfirmation"
                   Title="Delete Recurring Audit Schedule"
                   Message="@deleteConfirmationMessage"
                   Icon="bi bi-exclamation-triangle text-warning"
                   ConfirmText="Delete"
                   ConfirmIcon="bi bi-trash"
                   ConfirmButtonClass="btn-danger"
                   IsProcessing="isDeleting"
                   OnConfirm="ConfirmDelete"
                   OnCancel="CancelDelete" />

<ConfirmationDialog IsVisible="showToggleConfirmation"
                   Title="@toggleConfirmationTitle"
                   Message="@toggleConfirmationMessage"
                   Icon="@toggleConfirmationIcon"
                   ConfirmText="@toggleConfirmationAction"
                   ConfirmIcon="@toggleConfirmationButtonIcon"
                   ConfirmButtonClass="@toggleConfirmationButtonClass"
                   IsProcessing="isToggling"
                   OnConfirm="ConfirmToggle"
                   OnCancel="CancelToggle" />

<ConfirmationDialog IsVisible="showGenerateConfirmation"
                   Title="Generate Audits Now"
                   Message="@generateConfirmationMessage"
                   Icon="bi bi-lightning text-info"
                   ConfirmText="Generate Now"
                   ConfirmIcon="bi bi-lightning"
                   ConfirmButtonClass="btn-info"
                   IsProcessing="isGenerating"
                   OnConfirm="ConfirmGenerate"
                   OnCancel="CancelGenerate" />

@code {
    private PaginatedResult<RecurringAuditSettingSummaryDto>? recurringAudits;
    private bool isLoading = true;
    private string searchTerm = string.Empty;
    private string selectedStatus = string.Empty;
    private string selectedTemplateId = string.Empty;
    private int currentPage = 1;
    private const int pageSize = 20;

    // Confirmation dialog state
    private bool showDeleteConfirmation = false;
    private bool showToggleConfirmation = false;
    private bool showGenerateConfirmation = false;
    private bool isDeleting = false;
    private bool isToggling = false;
    private bool isGenerating = false;

    private string pendingActionId = string.Empty;
    private bool pendingToggleStatus = false;

    private string deleteConfirmationMessage = string.Empty;
    private string toggleConfirmationTitle = string.Empty;
    private string toggleConfirmationMessage = string.Empty;
    private string toggleConfirmationIcon = string.Empty;
    private string toggleConfirmationAction = string.Empty;
    private string toggleConfirmationButtonIcon = string.Empty;
    private string toggleConfirmationButtonClass = string.Empty;
    private string generateConfirmationMessage = string.Empty;

    // General UI state
    private string? errorMessage;
    private string? successMessage;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Add keyboard shortcuts
            await JSRuntime.InvokeVoidAsync("addKeyboardShortcuts", DotNetObjectReference.Create(this));
        }
    }

    [JSInvokable]
    public async Task HandleKeyboardShortcut(string shortcut)
    {
        switch (shortcut)
        {
            case "F5":
            case "Ctrl+R":
                await RefreshData();
                break;
            case "Ctrl+N":
                CreateNewSetting();
                break;
        }
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            bool? isEnabled = selectedStatus switch
            {
                "true" => true,
                "false" => false,
                _ => null
            };

            int? templateId = int.TryParse(selectedTemplateId, out var tid) ? tid : null;

            recurringAudits = await RecurringAuditService.GetRecurringAuditSettingsAsync(
                currentPage, pageSize, searchTerm, isEnabled, templateId);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading recurring audit settings");
            errorMessage = "Failed to load recurring audit settings. Please refresh the page or try again.";
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task RefreshData()
    {
        await LoadData();
    }

    private async Task OnSearchChanged()
    {
        currentPage = 1;
        await LoadData();
    }

    private async Task OnFilterChanged()
    {
        currentPage = 1;
        await LoadData();
    }

    private async Task ClearFilters()
    {
        searchTerm = string.Empty;
        selectedStatus = string.Empty;
        selectedTemplateId = string.Empty;
        currentPage = 1;
        await LoadData();
    }

    private async Task ChangePage(int page)
    {
        if (page >= 1 && page <= (recurringAudits?.TotalPages ?? 1))
        {
            currentPage = page;
            await LoadData();
        }
    }

    private bool HasActiveFilters()
    {
        return !string.IsNullOrEmpty(searchTerm) ||
               !string.IsNullOrEmpty(selectedStatus) ||
               !string.IsNullOrEmpty(selectedTemplateId);
    }

    private void CreateNewSetting()
    {
        Navigation.NavigateTo("/recurring-audits/create");
    }

    private void ViewSetting(string id)
    {
        Navigation.NavigateTo($"/recurring-audits/{id}");
    }

    private void EditSetting(string id)
    {
        Navigation.NavigateTo($"/recurring-audits/{id}/edit");
    }

    private void ToggleSetting(string id, bool currentStatus)
    {
        var setting = recurringAudits?.Items?.FirstOrDefault(s => s.Id == id);
        if (setting != null)
        {
            pendingActionId = id;
            pendingToggleStatus = currentStatus;

            if (currentStatus) // Currently enabled, will disable
            {
                toggleConfirmationTitle = "Disable Recurring Audit Schedule";
                toggleConfirmationMessage = $"Are you sure you want to disable the recurring audit schedule '{setting.SettingName}'? This will stop automatic audit generation until re-enabled.";
                toggleConfirmationIcon = "bi bi-exclamation-triangle text-warning";
                toggleConfirmationAction = "Disable";
                toggleConfirmationButtonIcon = "bi bi-pause";
                toggleConfirmationButtonClass = "btn-warning";
            }
            else // Currently disabled, will enable
            {
                toggleConfirmationTitle = "Enable Recurring Audit Schedule";
                toggleConfirmationMessage = $"Are you sure you want to enable the recurring audit schedule '{setting.SettingName}'? This will resume automatic audit generation.";
                toggleConfirmationIcon = "bi bi-check-circle text-success";
                toggleConfirmationAction = "Enable";
                toggleConfirmationButtonIcon = "bi bi-play";
                toggleConfirmationButtonClass = "btn-success";
            }

            showToggleConfirmation = true;
        }
    }

    private async Task ConfirmToggle()
    {
        if (string.IsNullOrEmpty(pendingActionId)) return;

        try
        {
            isToggling = true;
            errorMessage = null;
            successMessage = null;

            var success = await RecurringAuditService.ToggleRecurringAuditSettingAsync(pendingActionId);
            if (success)
            {
                var action = pendingToggleStatus ? "disabled" : "enabled";
                successMessage = $"Recurring audit schedule {action} successfully.";
                await LoadData(); // Refresh the list
            }
            else
            {
                errorMessage = "Failed to update recurring audit schedule status. Please try again.";
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error toggling recurring audit setting {Id}", pendingActionId);
            errorMessage = "An error occurred while updating the recurring audit schedule status. Please try again.";
        }
        finally
        {
            isToggling = false;
            showToggleConfirmation = false;
            pendingActionId = string.Empty;
        }
    }

    private void CancelToggle()
    {
        showToggleConfirmation = false;
        pendingActionId = string.Empty;
    }

    private void GenerateNow(string id)
    {
        var setting = recurringAudits?.Items?.FirstOrDefault(s => s.Id == id);
        if (setting != null)
        {
            pendingActionId = id;
            generateConfirmationMessage = $"Are you sure you want to generate audits now for '{setting.SettingName}'? This will create up to 10 audit instances based on the current schedule.";
            showGenerateConfirmation = true;
        }
    }

    private async Task ConfirmGenerate()
    {
        if (string.IsNullOrEmpty(pendingActionId)) return;

        try
        {
            isGenerating = true;
            errorMessage = null;
            successMessage = null;

            var result = await RecurringAuditService.GenerateRecurringAuditsAsync(pendingActionId, false, 10);
            if (result.AuditsGenerated > 0)
            {
                successMessage = $"Successfully generated {result.AuditsGenerated} audit(s).";
                await LoadData(); // Refresh the list
            }
            else
            {
                successMessage = "No new audits were generated. The schedule may not be ready or all upcoming audits already exist.";
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error generating audits for setting {Id}", pendingActionId);
            errorMessage = "An error occurred while generating audits. Please try again.";
        }
        finally
        {
            isGenerating = false;
            showGenerateConfirmation = false;
            pendingActionId = string.Empty;
        }
    }

    private void CancelGenerate()
    {
        showGenerateConfirmation = false;
        pendingActionId = string.Empty;
    }

    private void DeleteSetting(string id)
    {
        var setting = recurringAudits?.Items?.FirstOrDefault(s => s.Id == id);
        if (setting != null)
        {
            pendingActionId = id;
            deleteConfirmationMessage = $"Are you sure you want to delete the recurring audit schedule '{setting.SettingName}'? This action cannot be undone and will stop all future audit generation for this schedule.";
            showDeleteConfirmation = true;
        }
    }

    private async Task ConfirmDelete()
    {
        if (string.IsNullOrEmpty(pendingActionId)) return;

        try
        {
            isDeleting = true;
            errorMessage = null;
            successMessage = null;

            var success = await RecurringAuditService.DeleteRecurringAuditSettingAsync(pendingActionId);
            if (success)
            {
                successMessage = "Recurring audit schedule deleted successfully.";
                await LoadData(); // Refresh the list
            }
            else
            {
                errorMessage = "Failed to delete recurring audit schedule. Please try again.";
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error deleting recurring audit setting {Id}", pendingActionId);
            errorMessage = "An error occurred while deleting the recurring audit schedule. Please try again.";
        }
        finally
        {
            isDeleting = false;
            showDeleteConfirmation = false;
            pendingActionId = string.Empty;
        }
    }

    private void CancelDelete()
    {
        showDeleteConfirmation = false;
        pendingActionId = string.Empty;
    }
}
