@typeparam TItem
@using System.Timers
@implements IDisposable

<div class="position-relative">
    <input type="text"
           class="form-control @CssClass"
           @bind="searchTerm"
           @bind:event="oninput"
           @bind:after="OnSearchTermChanged"
           @onfocus="OnInputFocus"
           @onblur="OnInputBlur"
           placeholder="@Placeholder"
           autocomplete="off"
           required="@Required" />

    @if (showDropdown && (filteredItems.Any() || isLoading))
    {
        <div class="dropdown-menu search-dropdown show position-absolute w-100" style="max-height: 200px; overflow-y: auto; z-index: 1050;">
            @if (isLoading)
            {
                <div class="dropdown-item-text text-center">
                    <div class="spinner-border spinner-border-sm me-2" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    Searching...
                </div>
            }
            else if (filteredItems.Any())
            {
                @foreach (var item in filteredItems.Take(MaxResults))
                {
                    <button type="button"
                            class="dropdown-item d-flex justify-content-between align-items-center"
                            @onclick="() => SelectItem(item)"
                            @onmousedown:preventDefault="true">
                        <div>
                            <div class="fw-medium">@GetPrimaryText(item)</div>
                            @if (!string.IsNullOrEmpty(GetSecondaryText(item)))
                            {
                                <small class="text-muted">@GetSecondaryText(item)</small>
                            }
                        </div>
                        @if (!string.IsNullOrEmpty(GetAdditionalInfo(item)))
                        {
                            <small class="text-muted">@GetAdditionalInfo(item)</small>
                        }
                    </button>
                }
            }
            else if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                <div class="dropdown-item-text text-muted">
                    No results found for "@searchTerm"
                </div>
            }
        </div>
    }
</div>

@code {
    // Parameters
    [Parameter] public string Placeholder { get; set; } = "Type to search...";
    [Parameter] public string CssClass { get; set; } = "";
    [Parameter] public bool Required { get; set; } = false;
    [Parameter] public int MaxResults { get; set; } = 5;
    [Parameter] public int DebounceMs { get; set; } = 300;
    
    // Search function - must be provided by parent
    [Parameter] public Func<string, int, Task<IEnumerable<TItem>>>? SearchFunction { get; set; }
    
    // Display functions - must be provided by parent
    [Parameter] public Func<TItem, string>? GetPrimaryText { get; set; }
    [Parameter] public Func<TItem, string>? GetSecondaryText { get; set; }
    [Parameter] public Func<TItem, string>? GetAdditionalInfo { get; set; }
    [Parameter] public Func<TItem, string>? GetDisplayText { get; set; }
    
    // Selection callback
    [Parameter] public EventCallback<TItem> OnItemSelected { get; set; }
    
    // Clear callback
    [Parameter] public EventCallback OnCleared { get; set; }
    
    // Two-way binding for selected item
    [Parameter] public TItem? SelectedItem { get; set; }
    [Parameter] public EventCallback<TItem?> SelectedItemChanged { get; set; }

    // Internal state
    private string searchTerm = string.Empty;
    private List<TItem> filteredItems = new();
    private bool showDropdown = false;
    private bool isLoading = false;
    private Timer? debounceTimer;

    protected override void OnInitialized()
    {
        // Validate required parameters
        if (SearchFunction == null)
            throw new ArgumentNullException(nameof(SearchFunction), "SearchFunction parameter is required");
        if (GetPrimaryText == null)
            throw new ArgumentNullException(nameof(GetPrimaryText), "GetPrimaryText parameter is required");
        
        // Set default functions if not provided
        GetSecondaryText ??= _ => string.Empty;
        GetAdditionalInfo ??= _ => string.Empty;
        GetDisplayText ??= GetPrimaryText;

        // Initialize debounce timer
        debounceTimer = new Timer(DebounceMs);
        debounceTimer.Elapsed += async (sender, e) => await PerformSearch();
        debounceTimer.AutoReset = false;
    }

    protected override void OnParametersSet()
    {
        // Update search term if selected item changes externally
        if (SelectedItem != null && GetDisplayText != null)
        {
            var displayText = GetDisplayText(SelectedItem);
            if (searchTerm != displayText)
            {
                searchTerm = displayText;
                StateHasChanged();
            }
        }
        else if (SelectedItem == null && !string.IsNullOrEmpty(searchTerm))
        {
            searchTerm = string.Empty;
            StateHasChanged();
        }
    }

    private async Task OnSearchTermChanged()
    {
        // Stop any existing timer
        debounceTimer?.Stop();

        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            await ClearSelection();
            return;
        }

        // Start debounce timer
        debounceTimer?.Start();
    }

    private async Task PerformSearch()
    {
        if (SearchFunction == null || string.IsNullOrWhiteSpace(searchTerm))
            return;

        try
        {
            await InvokeAsync(async () =>
            {
                isLoading = true;
                StateHasChanged();

                var results = await SearchFunction(searchTerm, MaxResults);
                filteredItems = results.ToList();
                showDropdown = true;
                isLoading = false;
                StateHasChanged();
            });
        }
        catch (Exception ex)
        {
            // Log error and clear results
            Console.WriteLine($"Search error: {ex.Message}");
            filteredItems.Clear();
            isLoading = false;
            showDropdown = false;
            await InvokeAsync(StateHasChanged);
        }
    }

    private void OnInputFocus()
    {
        if (filteredItems.Any())
        {
            showDropdown = true;
        }
    }

    private async Task OnInputBlur()
    {
        // Delay hiding dropdown to allow for click events
        await Task.Delay(150);
        showDropdown = false;
        StateHasChanged();
    }

    private async Task SelectItem(TItem item)
    {
        SelectedItem = item;
        searchTerm = GetDisplayText?.Invoke(item) ?? GetPrimaryText?.Invoke(item) ?? string.Empty;
        showDropdown = false;
        
        // Notify parent components
        await SelectedItemChanged.InvokeAsync(item);
        await OnItemSelected.InvokeAsync(item);
        
        StateHasChanged();
    }

    private async Task ClearSelection()
    {
        SelectedItem = default(TItem);
        filteredItems.Clear();
        showDropdown = false;
        
        // Notify parent components
        await SelectedItemChanged.InvokeAsync(default(TItem));
        await OnCleared.InvokeAsync();
        
        StateHasChanged();
    }

    public void Dispose()
    {
        debounceTimer?.Dispose();
    }
}
