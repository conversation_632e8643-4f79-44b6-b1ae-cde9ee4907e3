@using HWSAuditPlatform.WebApp.Models
@using HWSAuditPlatform.WebApp.Services
@inject IFindingCategoryApiService FindingCategoryApiService
@inject ILogger<FindingCategoryList> Logger

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">Finding Categories</h5>
        @if (AuditTemplateId.HasValue)
        {
            <button class="btn btn-primary btn-sm" @onclick="ShowCreateModal">
                <i class="fas fa-plus"></i> Add Category
            </button>
        }
    </div>
    <div class="card-body">
        @if (isLoading)
        {
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        }
        else if (!AuditTemplateId.HasValue)
        {
            <div class="text-center text-muted">
                <i class="fas fa-tags fa-3x mb-3"></i>
                <p>Select an audit template to manage finding categories.</p>
            </div>
        }
        else if (categories.Any())
        {
            <div class="row">
                @foreach (var category in categories.OrderBy(c => c.DisplayOrder).ThenBy(c => c.CategoryName))
                {
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card h-100 category-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div class="d-flex align-items-center">
                                        @if (!string.IsNullOrEmpty(category.ColorCode))
                                        {
                                            <div class="category-color-indicator me-2" style="background-color: @category.ColorCode;"></div>
                                        }
                                        @if (!string.IsNullOrEmpty(category.IconName))
                                        {
                                            <i class="@GetIconClass(category.IconName) me-2"></i>
                                        }
                                        <h6 class="card-title mb-0">@category.CategoryName</h6>
                                    </div>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" @onclick="() => EditCategory(category)">
                                                <i class="fas fa-edit me-2"></i>Edit
                                            </a></li>
                                            <li><a class="dropdown-item text-danger" href="#" @onclick="() => DeleteCategory(category)">
                                                <i class="fas fa-trash me-2"></i>Delete
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>

                                @if (!string.IsNullOrEmpty(category.Description))
                                {
                                    <p class="card-text text-muted small">@category.Description</p>
                                }

                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <span class="badge bg-light text-dark">Order: @category.DisplayOrder</span>
                                        @if (!category.IsActive)
                                        {
                                            <span class="badge bg-danger">Inactive</span>
                                        }
                                        @if (category.RequiresDocumentation)
                                        {
                                            <span class="badge bg-info">Requires Docs</span>
                                        }
                                    </div>
                                </div>

                                @if (ShowUsageStatistics && (category.FindingCount > 0 || category.OpenFindingCount > 0))
                                {
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-chart-bar me-1"></i>
                                            @category.FindingCount findings (@category.OpenFindingCount open)
                                        </small>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="text-center text-muted">
                <i class="fas fa-tags fa-3x mb-3"></i>
                <p>No finding categories configured for this template.</p>
                <button class="btn btn-primary" @onclick="ShowCreateModal">
                    <i class="fas fa-plus"></i> Add First Category
                </button>
            </div>
        }
    </div>
</div>

@if (showCreateModal && AuditTemplateId.HasValue)
{
    <FindingCategoryCreateModal 
        @bind-IsVisible="showCreateModal"
        AuditTemplateId="AuditTemplateId.Value"
        OnCategoryCreated="OnCategoryCreated" />
}

<style>
    .category-card {
        border-left: 4px solid var(--bs-primary);
        transition: transform 0.2s ease-in-out;
    }

    .category-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .category-color-indicator {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        border: 1px solid #dee2e6;
    }
</style>

@code {
    [Parameter] public int? AuditTemplateId { get; set; }
    [Parameter] public bool ShowUsageStatistics { get; set; } = false;
    [Parameter] public EventCallback OnCategoryChanged { get; set; }

    private List<FindingCategoryModel> categories = new();
    private bool isLoading = true;
    private bool showCreateModal = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadCategories();
    }

    protected override async Task OnParametersSetAsync()
    {
        await LoadCategories();
    }

    private async Task LoadCategories()
    {
        if (!AuditTemplateId.HasValue)
        {
            categories = new List<FindingCategoryModel>();
            isLoading = false;
            return;
        }

        try
        {
            isLoading = true;
            categories = await FindingCategoryApiService.GetFindingCategoriesForTemplateAsync(
                AuditTemplateId.Value, ShowUsageStatistics);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading finding categories for template {TemplateId}", AuditTemplateId);
            categories = new List<FindingCategoryModel>();
        }
        finally
        {
            isLoading = false;
        }
    }

    private void ShowCreateModal()
    {
        showCreateModal = true;
    }

    private async Task OnCategoryCreated()
    {
        showCreateModal = false;
        await LoadCategories();
        await OnCategoryChanged.InvokeAsync();
    }

    private async Task EditCategory(FindingCategoryModel category)
    {
        // TODO: Implement edit functionality
        Logger.LogInformation("Edit category {Id} requested", category.Id);
    }

    private async Task DeleteCategory(FindingCategoryModel category)
    {
        // TODO: Implement delete functionality with confirmation
        Logger.LogInformation("Delete category {Id} requested", category.Id);
    }

    private static string GetIconClass(string iconName)
    {
        return iconName switch
        {
            "warning-triangle" => "fas fa-exclamation-triangle",
            "exclamation-circle" => "fas fa-exclamation-circle",
            "info-circle" => "fas fa-info-circle",
            "shield-alt" => "fas fa-shield-alt",
            "tools" => "fas fa-tools",
            "cog" => "fas fa-cog",
            "bug" => "fas fa-bug",
            "fire" => "fas fa-fire",
            "bolt" => "fas fa-bolt",
            "eye" => "fas fa-eye",
            _ => "fas fa-tag"
        };
    }
}
