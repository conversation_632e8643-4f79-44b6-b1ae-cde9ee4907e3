@page "/templates"
@attribute [Authorize]
@implements IDisposable

<PageTitle>Templates - HWS Audit Platform</PageTitle>

<div class="container-fluid mt-4">
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-1">
                        <i class="bi bi-file-earmark-text me-2 text-success"></i>
                        Template Management
                    </h1>
                    <p class="text-muted mb-0">Create and manage audit templates</p>
                </div>
                <div>
                    <button class="btn btn-success" @onclick="CreateNewTemplate">
                        <i class="bi bi-plus-circle me-2"></i>Create Template
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="input-group">
                <span class="input-group-text"><i class="bi bi-search"></i></span>
                <input type="text" class="form-control" placeholder="Search templates..." @bind="searchTerm" @bind:event="oninput" @bind:after="OnSearchTermChanged" @onkeypress="@(async (e) => { if (e.Key == "Enter") await SearchTemplates(); })" />
                <button class="btn btn-outline-secondary" @onclick="SearchTemplates">Search</button>
            </div>
        </div>
        <div class="col-md-3">
            <select class="form-select" @bind="statusFilter" @bind:after="OnStatusFilterChanged">
                <option value="">All Templates</option>
                <option value="true">Active</option>
                <option value="false">Inactive</option>
            </select>
        </div>
        <div class="col-md-3">
            <button class="btn btn-outline-secondary w-100" @onclick="ClearFilters">
                <i class="bi bi-x-circle me-1"></i>Clear Filters
            </button>
        </div>
    </div>

    <!-- Template List -->
    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-success" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading templates...</p>
        </div>
    }
    else if (templates?.Any() == true)
    {
        <div class="row">
            @foreach (var template in templates)
            {
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">@template.TemplateName</h6>
                            <div class="d-flex gap-2">
                                <span class="badge @(template.IsPublished ? "bg-success" : "bg-warning")">
                                    @(template.IsPublished ? "Published" : "Draft")
                                </span>
                                @if (!template.IsActive)
                                {
                                    <span class="badge bg-secondary">Inactive</span>
                                }
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="card-text">
                                @if (!string.IsNullOrEmpty(template.Description))
                                {
                                    @template.Description
                                }
                                else
                                {
                                    <em class="text-muted">No description available</em>
                                }
                            </p>
                            <div class="mt-3">
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">
                                            <strong>Version:</strong> @template.Version<br />
                                            <strong>Questions:</strong> @GetQuestionCount(template)
                                        </small>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">
                                            <strong>Created:</strong> @template.CreatedAt.ToString("MMM dd")<br />
                                            @if (template.UpdatedAt != default(DateTime))
                                            {
                                                <strong>Updated:</strong> @template.UpdatedAt.ToString("MMM dd")
                                            }
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100" role="group">
                                <button class="btn btn-outline-primary btn-sm" @onclick="() => ViewTemplate(template.Id)">
                                    <i class="bi bi-eye me-1"></i>View
                                </button>
                                @if (template.IsPublished)
                                {
                                    <button class="btn btn-outline-success btn-sm" @onclick="() => CreateNewVersion(template.Id)">
                                        <i class="bi bi-plus-circle me-1"></i>New Version
                                    </button>
                                }
                                else
                                {
                                    <button class="btn btn-outline-secondary btn-sm" @onclick="() => EditTemplate(template.Id)">
                                        <i class="bi bi-pencil me-1"></i>Edit
                                    </button>
                                }
                                <div class="btn-group" role="group">
                                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        @if (!template.IsPublished)
                                        {
                                            <li>
                                                <button class="dropdown-item" @onclick="() => ManageQuestions(template.Id)">
                                                    <i class="bi bi-question-circle me-2"></i>Manage Questions
                                                </button>
                                            </li>
                                            <li>
                                                <button class="dropdown-item text-success" @onclick="() => PublishTemplate(template.Id)">
                                                    <i class="bi bi-check-circle me-2"></i>Publish Template
                                                </button>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>
                                        }
                                        <li>
                                            <button class="dropdown-item" @onclick="() => DuplicateTemplate(template.Id)">
                                                <i class="bi bi-files me-2"></i>Duplicate
                                            </button>
                                        </li>
                                        <li>
                                            <button class="dropdown-item" @onclick="() => ViewVersionHistory(template.Id)">
                                                <i class="bi bi-clock-history me-2"></i>Version History
                                            </button>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        @if (template.IsActive)
                                        {
                                            <li>
                                                <button class="dropdown-item text-warning" @onclick="() => DeactivateTemplate(template.Id)">
                                                    <i class="bi bi-pause-circle me-2"></i>Deactivate
                                                </button>
                                            </li>
                                        }
                                        else
                                        {
                                            <li>
                                                <button class="dropdown-item text-success" @onclick="() => ActivateTemplate(template.Id)">
                                                    <i class="bi bi-play-circle me-2"></i>Activate
                                                </button>
                                            </li>
                                        }
                                        <li>
                                            <button class="dropdown-item text-danger" @onclick="() => DeleteTemplate(template.Id)">
                                                <i class="bi bi-trash me-2"></i>Delete
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="bi bi-file-earmark-x display-1 text-muted"></i>
            <h3 class="mt-3">No templates found</h3>
            <p class="text-muted">Create your first template to get started.</p>
            <button class="btn btn-success" @onclick="CreateNewTemplate">
                <i class="bi bi-plus-circle me-2"></i>Create Template
            </button>
        </div>
    }
</div>

@code {
    private IEnumerable<AuditTemplate>? templates;
    private bool isLoading = true;
    private string searchTerm = string.Empty;
    private string statusFilter = string.Empty;
    private Timer? _searchTimer;
    private string? successMessage;
    private string? errorMessage;

    [Inject] private ITemplateApiService TemplateService { get; set; } = default!;
    [Inject] private NavigationManager Navigation { get; set; } = default!;
    [Inject] private ILogger<TemplateList> Logger { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        await LoadTemplates();
    }

    private async Task LoadTemplates()
    {
        try
        {
            isLoading = true;

            // Parse status filter
            bool? isActiveFilter = statusFilter switch
            {
                "true" => true,
                "false" => false,
                _ => null
            };

            // Apply search and filter parameters
            var searchParam = string.IsNullOrWhiteSpace(searchTerm) ? null : searchTerm;
            templates = await TemplateService.GetTemplatesAsync(searchParam, isActiveFilter);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading templates");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SearchTemplates()
    {
        await LoadTemplates();
    }

    private async Task OnStatusFilterChanged()
    {
        await LoadTemplates();
    }

    private void OnSearchTermChanged()
    {
        // Debounce search to avoid too many API calls
        _searchTimer?.Dispose();
        _searchTimer = new Timer(async _ =>
        {
            await InvokeAsync(async () =>
            {
                await LoadTemplates();
                StateHasChanged();
            });
        }, null, TimeSpan.FromMilliseconds(500), Timeout.InfiniteTimeSpan);
    }

    private async Task ClearFilters()
    {
        searchTerm = string.Empty;
        statusFilter = string.Empty;
        await LoadTemplates();
    }

    private void CreateNewTemplate()
    {
        Navigation.NavigateTo("/templates/create");
    }

    private void ViewTemplate(int templateId)
    {
        Navigation.NavigateTo($"/templates/{templateId}");
    }

    private void EditTemplate(int templateId)
    {
        Navigation.NavigateTo($"/templates/{templateId}/edit");
    }

    private void ManageQuestions(int templateId)
    {
        Navigation.NavigateTo($"/templates/{templateId}/questions");
    }

    private void DuplicateTemplate(int templateId)
    {
        Navigation.NavigateTo($"/templates/{templateId}/duplicate");
    }

    private async Task ActivateTemplate(int templateId)
    {
        // TODO: Implement activate template API call
        await LoadTemplates();
    }

    private async Task DeactivateTemplate(int templateId)
    {
        // TODO: Implement deactivate template API call
        await LoadTemplates();
    }

    private async Task DeleteTemplate(int templateId)
    {
        if (await TemplateService.DeleteTemplateAsync(templateId))
        {
            await LoadTemplates();
        }
    }

    private int GetQuestionCount(AuditTemplate template)
    {
        if (template.QuestionGroups?.Any() == true)
        {
            return template.QuestionGroups.Sum(g => g.Questions?.Count(q => q.IsActive) ?? 0) +
                   template.Questions.Count(q => q.IsActive && !q.QuestionGroupId.HasValue);
        }
        return template.Questions?.Count(q => q.IsActive) ?? 0;
    }

    private async Task CreateNewVersion(int templateId)
    {
        try
        {
            var newTemplateId = await TemplateService.CreateTemplateVersionAsync(templateId);
            if (newTemplateId.HasValue)
            {
                Navigation.NavigateTo($"/templates/{newTemplateId.Value}/edit");
            }
            else
            {
                Logger.LogWarning("Failed to create template version for template {TemplateId}", templateId);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating template version for template {TemplateId}", templateId);
        }
    }

    private async Task PublishTemplate(int templateId)
    {
        try
        {
            var result = await TemplateService.PublishTemplateWithDetailsAsync(templateId);
            if (result.IsSuccess)
            {
                await LoadTemplates(); // Refresh the list to show updated status
                successMessage = "Template published successfully!";
            }
            else
            {
                if (result.IsValidationError)
                {
                    errorMessage = $"Cannot publish template: {result.GetErrorMessage()}";
                }
                else
                {
                    errorMessage = $"Failed to publish template: {result.GetErrorMessage()}";
                }
                Logger.LogWarning("Failed to publish template {TemplateId}: {Error}", templateId, result.GetErrorMessage());
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error publishing template {TemplateId}", templateId);
            errorMessage = "An error occurred while publishing the template.";
        }
    }

    private void ViewVersionHistory(int templateId)
    {
        Navigation.NavigateTo($"/templates/{templateId}/versions");
    }

    public void Dispose()
    {
        _searchTimer?.Dispose();
    }
}
